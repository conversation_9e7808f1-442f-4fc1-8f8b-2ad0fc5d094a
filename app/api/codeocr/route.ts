import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    // Check if user is authenticated
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // Get the form data from the request
    const formData = await req.formData();

    // Forward the request to the FastAPI backend
    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    const response = await fetch(`${backendUrl}/api/codeocr/`, {
      method: "POST",
      body: formData,
    });

    // Parse the response from the backend
    const result = await response.json();

    // Check if the backend request was successful
    if (response.ok && result.code === 0) {
      return respData(result.data);
    } else {
      return respErr(result.message || "Failed to extract code from image");
    }
  } catch (error) {
    console.error("Error in codeocr API:", error);
    return respErr("Failed to process image");
  }
}
