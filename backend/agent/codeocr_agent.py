"""CodeOCR agent."""

import base64
import os
from typing import Annotated, Literal, Optional, Union

from dotenv import load_dotenv
from fastapi import UploadFile
from langchain_core.messages import AnyMessage
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.graph.state import CompiledStateGraph
from openai import OpenAI
from pydantic import BaseModel, Field

load_dotenv(dotenv_path="../../.env.development", override=True)


client = OpenAI(
    api_key=os.environ["OPENAI_API_KEY"],
    base_url=os.environ["OPENAI_API_BASE"],
)


class State(BaseModel):
    """Represent the state of the code extraction process.

    This class maintains the current state of the code extraction workflow, including
    the processing status, corresponding programming language, extracted code content,
    and the conversation messages between the user and AI.

    Attributes:
        status: Current processing status (e.g., "pending", "completed", "error").
        image: The image containing the code.
        language: The programming language of the extracted code specified by the user
            or detected by AI.
        code: The code content extracted from the image.
        messages: A list of conversation messages exchanged between the human user
            and AI assistant during the code extraction process.
    """

    status: Literal["pending", "completed", "error"] = Field(
        ..., description="Current processing status."
    )
    image: Optional[Union[UploadFile, str]] = Field(
        ..., description="The image containing the code."
    )
    language: Optional[str] = Field(
        ..., description="Detected or specified programming language of the code."
    )
    code: Optional[str] = Field(
        ..., description="The code content extracted from the image."
    )
    messages: Annotated[list[AnyMessage], add_messages] = Field(
        ..., description="A list of conversation messages."
    )


async def codeocr(state: State) -> State:
    """Extracts code from an image.

    Args:
        state: A State object containing the image to extract code from.

    Returns:
        The updated state object with the 'markdown' field populated with the
            markdown content fetched from the URL.
    """
    image = state.image

    image_content = await image.read()
    image_base64 = base64.b64encode(image_content).decode("utf-8")

    response = client.chat.completions.create(
        model="glm-4v-flash",
        messages=[
            {
                "role": "user",
                "content": [
                    {"type": "image_url", "image_url": {"url": image_base64}},
                    {
                        "type": "text",
                        "text": "Extract code from this image.",
                    },
                ],
            }
        ],
    )
    state.code = response.choices[0].message.content
    return state


def make_codeocr_agent() -> CompiledStateGraph:
    """Create a workflow graph for extracting code from images."""
    workflow = StateGraph(State)

    workflow.add_node(codeocr)

    workflow.add_edge(START, "codeocr")
    workflow.add_edge("codeocr", END)

    graph = workflow.compile(name="codeocr")

    return graph
