"""CodeOCR API."""

from typing import Optional

import uvicorn
from fastapi import FastAPI, File, Form, UploadFile
from fastapi.middleware.cors import CORSMiddleware

from .agent import codeocr_agent

app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

agent = codeocr_agent.make_codeocr_agent()


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/codeocr", response_model=codeocr_agent.State)
async def codeocr(file: UploadFile = File(...), language: Optional[str] = Form(None)):
    """Convert code screenshot to editable code.

    Args:
        file: The image file containing code.
        language: Optional programming language to help with extraction.

    Returns:
        JSON response with extracted code and detected language.
    """
    state = codeocr_agent.State(status="pending", image=file, language=language)
    response = await agent.ainvoke(state)
    return response


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
